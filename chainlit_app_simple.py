"""Chainlit + LangGraph Copilot with Plotly support
====================================================
Version corrigée - Résout les problèmes d'affichage des graphiques,
améliore le streaming et la gestion des erreurs.
"""

###############################################################################
# 0. Standard library imports & environment tweaks
###############################################################################

import os
import sys
import uuid
import json
import pickle
import asyncio
import pprint
from typing import Dict, List, Any, Optional
import traceback
# Make sure Python streams are unbuffered so print() shows up immediately.
# (Works only when the process is launched by `python -u`, but we do our part.)
os.environ.setdefault("PYTHONUNBUFFERED", "1")

###############################################################################
# 1. Third‑party imports  
###############################################################################

from dotenv import load_dotenv

import chainlit as cl
from chainlit.types import ThreadDict
from chainlit.data.sql_alchemy import SQLAlchemyDataLayer

from langchain_core.messages import HumanMessage, ToolMessage, AIMessage, AIMessageChunk
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver

# Local modules (your original project structure)
from main_copilot import (
    create_agent,
    make_postgres_checkpointer,  # still used elsewhere
    AgentState,
    InputData,
    _merge,
    ensure_message_ids,
    remove_duplicate_messages,
    get_prompt_for_partner,
)
from sandbox_client import SandboxClient
from plot_template import apply_company_style  # Import the plot styling function

###############################################################################
# 2. Environment & data‑layer initialisation
###############################################################################

print("Attempting to load .env from CWD…")
load_dotenv(override=True)

DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")
DB_NAME = os.getenv("POSTGRES_DB", "chainlit_db")
DB_USER = os.getenv("POSTGRES_USER", "user_test")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "password_test")
DB_URI_LANGGRAPH = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

@cl.data_layer
def get_data_layer():
    conninfo = (
        "postgresql+asyncpg://"
        f"{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    )
    print("[DEBUG CL DATA_LAYER] Initialising SQLAlchemyDataLayer…")
    return SQLAlchemyDataLayer(conninfo=conninfo, storage_provider=None)

###############################################################################
# 3. OAuth callback
###############################################################################

@cl.oauth_callback
def oauth_callback(
    provider_id: str,
    token: str,
    raw_user_data: Dict[str, str],
    default_user: cl.User,
) -> Optional[cl.User]:
    print(f"OAuth callback for provider: {provider_id}")
    return default_user

###############################################################################
# 4. Helpers
###############################################################################

def _parse_tool_content(content: Any) -> Dict[str, Any]:
    if isinstance(content, str):
        try: return json.loads(content)
        except json.JSONDecodeError: return {"raw_content": content, "error": "Not valid JSON"}
    if isinstance(content, dict): return content
    return {"raw_content": str(content), "error": "Unknown content type"}

def serialise_state(state: AgentState) -> dict:
    """Make AgentState printable (messages → small dicts)."""
    def _msg_to_dict(m):
        if isinstance(m, (HumanMessage, AIMessage, ToolMessage)):
            return {
                "type": m.__class__.__name__,
                "id": getattr(m, "id", None),
                "content": m.content if isinstance(m.content, str) else "<complex>",
            }
        return str(m)

    # Create a copy of the state to avoid modifying the original
    serialized = dict(state)
    
    # Handle messages specially
    if "messages" in serialized:
        serialized["messages"] = [_msg_to_dict(x) for x in serialized["messages"]]
    
    # Ensure output_image_paths is preserved
    if "output_image_paths" in serialized:
        serialized["output_image_paths"] = list(serialized["output_image_paths"])
    
    return serialized

###############################################################################
# 5. LangGraph initialisation helper 
###############################################################################

async def initialize_langgraph_components(thread_id: str, partner_name: str):
    """Create/restore checkpointer + agent + state for the given thread."""
    print(f"[DEBUG LG] Initialising components for thread_id={thread_id}")

    # 5.1  Checkpointer context manager
    checkpointer_cm = AsyncPostgresSaver.from_conn_string(DB_URI_LANGGRAPH)
    try:
        cp_instance = await checkpointer_cm.__aenter__()
        await cp_instance.setup()
    except Exception as exc:
        print(f"[ERROR LG] Checkpointer setup failed: {exc}")
        cp_instance = None

    cl.user_session.set("lg_checkpointer_cm", checkpointer_cm)
    cl.user_session.set("lg_checkpointer_instance", cp_instance)

    # 5.2  Agent
    if cp_instance:
        lg_agent = create_agent(checkpointer=cp_instance, partner=partner_name) 
        cl.user_session.set("lg_agent", lg_agent)
    else:
        cl.user_session.set("lg_agent", None)

    # 5.3  Agent state
    if cp_instance:
        cfg = RunnableConfig(configurable={"thread_id": thread_id})
        try:
            persisted = await cp_instance.aget(cfg) or {}
        except Exception:
            persisted = {}
    else:
        persisted = {}

    if persisted:
        # rebuild messages list into proper objects
        rebuilt: list[Any] = []
        for md in persisted.get("messages", []):
            if isinstance(md, (HumanMessage, AIMessage, ToolMessage)):
                rebuilt.append(md)
            elif isinstance(md, dict):
                typ = md.get("type", "").lower()
                if "human" in typ:
                    rebuilt.append(HumanMessage(**md))
                elif "ai" in typ:
                    rebuilt.append(AIMessage(**md))
                elif "tool" in typ:
                    rebuilt.append(ToolMessage(**md))
        curr_state: AgentState = {
            **persisted,
            "messages": rebuilt,
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner_name,
        }
    else:
        curr_state = {
            "messages": [],
            "remaining_steps": 25,
            "input_data": [],
            "intermediate_outputs": [],
            "current_variables": {},
            "output_image_paths": [],
            "data_description": [],
            "generic_parser_request": [],
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner_name,
            "partner_config": {},
            "summary": "",
            "id_last_summary": None,
        }

    cl.user_session.set("lg_agent_state", curr_state)
    cl.user_session.set("thread_id", thread_id)
    cl.user_session.set("langgraph_initialized_for_thread", bool(cp_instance))

###############################################################################
# 6. Chat‑lifecycle callbacks
###############################################################################

@cl.on_chat_start
async def on_chat_start():
    pn = os.getenv("DEFAULT_PARTNER", "oksigen")
    print(f"[DEBUG CL] on_chat_start: partner_name={pn}")
    cl.user_session.set("partner_name", pn)
    cl.user_session.set("langgraph_initialized_for_thread", False)
    cl.user_session.set("displayed_plot_filenames", set())  # Initialize empty set for tracking displayed plots
    await cl.Message(content=f"Agent initialisé (Partenaire {pn}). Dites‑moi …").send()

@cl.on_chat_resume
async def on_chat_resume(thread: ThreadDict):
    tid = thread["id"]
    pn = thread.get("metadata", {}).get("partner_name", os.getenv("DEFAULT_PARTNER", "oksigen"))
    await initialize_langgraph_components(tid, pn)
    st = cl.user_session.get("lg_agent_state", {"messages": []})
    cl.user_session.set("displayed_plot_filenames", set())  # Reset displayed plots set on resume
    await cl.Message(
        content=f"Conversation reprise (Partenaire {pn}). {len(st.get('messages', []))} messages enregistrés."
    ).send()

###############################################################################
# 7. Main message handler constants
###############################################################################

# ### NEW/MODIFIED SECTION START ###

###############################################################################
# 8. Main message handler
###############################################################################

# Tool name mapping for better display in the UI
TOOL_DISPLAY_NAMES = {
    "generic_parser": "🔍 Parsing Data",
    "complete_python_task": "🐍 Executing Python Code",
    # Add other tool names here
}

def get_tool_display_name(tool_name: str) -> str:
    """Returns a user-friendly name for a tool, or the original name if not found."""
    return TOOL_DISPLAY_NAMES.get(tool_name, tool_name)

##############################################Plot tracking##########################################
import hashlib
import os
import pickle
import json
import sys
import traceback
import uuid
from typing import Dict, Set, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

import chainlit as cl
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from langchain_core.runnables import RunnableConfig

@dataclass
class PlotInfo:
    """Enhanced plot information tracking"""
    path: str
    content_hash: str
    timestamp: datetime
    turn_id: str
    display_name: str
    
    def __hash__(self):
        return hash((self.path, self.content_hash))
    
    def __eq__(self, other):
        if not isinstance(other, PlotInfo):
            return False
        return self.path == other.path and self.content_hash == other.content_hash

class PlotTracker:
    """Enhanced plot tracking with deduplication and versioning"""
    
    def __init__(self):
        self.displayed_plots: Dict[str, PlotInfo] = {}  # path -> PlotInfo
        self.content_hashes: Dict[str, str] = {}  # content_hash -> path
        self.turn_plots: Dict[str, List[PlotInfo]] = {}  # turn_id -> list of plots
    
    def get_plot_hash(self, pickle_bytes: bytes) -> str:
        """Generate content hash from plot pickle bytes"""
        return hashlib.sha256(pickle_bytes).hexdigest()[:16]
    
    def is_duplicate_content(self, content_hash: str) -> bool:
        """Check if plot content already exists"""
        return content_hash in self.content_hashes
    
    def should_display_plot(self, plot_path: str, content_hash: str) -> bool:
        """Determine if plot should be displayed based on path and content"""
        # Check if exact same plot (path + content) was already displayed
        if plot_path in self.displayed_plots:
            existing_plot = self.displayed_plots[plot_path]
            if existing_plot.content_hash == content_hash:
                return False
        
        # Check if same content exists with different path
        if self.is_duplicate_content(content_hash):
            return False
            
        return True
    
    def add_plot(self, plot_path: str, content_hash: str, turn_id: str) -> PlotInfo:
        """Add new plot to tracking system"""
        display_name = os.path.basename(plot_path).rsplit(".", 1)[0]
        
        plot_info = PlotInfo(
            path=plot_path,
            content_hash=content_hash,
            timestamp=datetime.now(),
            turn_id=turn_id,
            display_name=display_name
        )
        
        # Update tracking dictionaries
        self.displayed_plots[plot_path] = plot_info
        self.content_hashes[content_hash] = plot_path
        
        # Track plots by turn
        if turn_id not in self.turn_plots:
            self.turn_plots[turn_id] = []
        self.turn_plots[turn_id].append(plot_info)
        
        return plot_info
    
    def get_latest_plots(self, limit: int = 10) -> List[PlotInfo]:
        """Get the most recently generated plots"""
        all_plots = list(self.displayed_plots.values())
        return sorted(all_plots, key=lambda p: p.timestamp, reverse=True)[:limit]
    
    def get_turn_plots(self, turn_id: str) -> List[PlotInfo]:
        """Get all plots generated in a specific turn"""
        return self.turn_plots.get(turn_id, [])
    
    def clear_old_plots(self, keep_last_n_turns: int = 5):
        """Clean up old plot tracking data"""
        if len(self.turn_plots) <= keep_last_n_turns:
            return
        
        # Get turns sorted by timestamp (newest first)
        turns_by_time = []
        for turn_id, plots in self.turn_plots.items():
            if plots:
                latest_timestamp = max(p.timestamp for p in plots)
                turns_by_time.append((turn_id, latest_timestamp))
        
        turns_by_time.sort(key=lambda x: x[1], reverse=True)
        
        # Keep only the latest N turns
        turns_to_keep = set(turn_id for turn_id, _ in turns_by_time[:keep_last_n_turns])
        
        # Remove old plots from tracking
        plots_to_remove = []
        for plot_path, plot_info in self.displayed_plots.items():
            if plot_info.turn_id not in turns_to_keep:
                plots_to_remove.append(plot_path)
        
        for plot_path in plots_to_remove:
            plot_info = self.displayed_plots.pop(plot_path)
            self.content_hashes.pop(plot_info.content_hash, None)
        
        # Clean up turn tracking
        for turn_id in list(self.turn_plots.keys()):
            if turn_id not in turns_to_keep:
                del self.turn_plots[turn_id]

async def handle_plots_with_tracker(sandbox_client, active_thread_id: str, plot_paths: List[str], 
                                  plot_tracker: PlotTracker, turn_id: str) -> List:
    """Enhanced plot handling with PlotTracker"""
    all_plot_elements = []
    
    for plot_path in plot_paths:
        try:
            # Download plot data
            pickle_bytes = await sandbox_client.download_plot(
                session_id=active_thread_id, 
                plot_name=plot_path
            )
            
            if not pickle_bytes:
                print(f"[WARNING] No data received for plot: {plot_path}")
                continue
            
            # Generate content hash
            content_hash = plot_tracker.get_plot_hash(pickle_bytes)
            
            # Check if we should display this plot
            if not plot_tracker.should_display_plot(plot_path, content_hash):
                print(f"[SKIP] Duplicate plot detected: {plot_path} (hash: {content_hash})")
                continue
            
            # Add to tracker
            plot_info = plot_tracker.add_plot(plot_path, content_hash, turn_id)
            
            # Create plot element
            fig_obj = pickle.loads(pickle_bytes)
            fig_obj = apply_company_style(fig_obj)
            plot_elem = cl.Plotly(
                name=f"{plot_info.display_name}_{content_hash[:8]}", 
                figure=fig_obj, 
                display="inline"
            )
            all_plot_elements.append(plot_elem)
            
            print(f"[PLOT] Added new plot: {plot_path} (hash: {content_hash})")
            
        except Exception as e:
            print(f"[ERROR PLOT] Failed to process {plot_path}: {e}")
    
    return all_plot_elements

@cl.on_message
async def on_message(msg_event: cl.Message):
    active_thread_id = cl.context.session.thread_id
    if not active_thread_id:
        print(f"[CRITICAL CL] cl.context.session.thread_id is None in on_message!")
        await cl.Message(content="Erreur critique: Impossible d'identifier la session.").send()
        cl.user_session.set("langgraph_initialized_for_thread", False)
        return

    # Initialize or get plot tracker
    plot_tracker = cl.user_session.get("plot_tracker")
    if not plot_tracker:
        plot_tracker = PlotTracker()
        cl.user_session.set("plot_tracker", plot_tracker)
    
    # Generate unique turn ID for this conversation turn
    turn_id = f"{active_thread_id}_{msg_event.id}"

    if not cl.user_session.get("langgraph_initialized_for_thread") or \
       cl.user_session.get("thread_id") != active_thread_id:
        print(f"[DEBUG CL] Initializing/Re-syncing LangGraph for thread_id: {active_thread_id}")
        partner_name_for_init = cl.user_session.get("partner_name", os.getenv("DEFAULT_PARTNER", "oksigen"))
        await initialize_langgraph_components(active_thread_id, partner_name_for_init)

    lg_agent = cl.user_session.get("lg_agent")
    lg_agent_state: Optional[AgentState] = cl.user_session.get("lg_agent_state")
    partner_name = cl.user_session.get("partner_name")

    print(f"\n[DEBUG CL] === Turn Start for Thread: {active_thread_id}, User Msg ID: {msg_event.id} ===")
    print(f"[DEBUG CL] Current turn ID: {turn_id}")

    sandbox_client: Optional[SandboxClient] = cl.user_session.get("sandbox_client")
    if not sandbox_client:
        try:
            sandbox_client = SandboxClient()
            cl.user_session.set("sandbox_client", sandbox_client)
        except Exception as e:
            await cl.Message(content="Erreur de configuration du client Sandbox.").send()
            return

    if not lg_agent or not lg_agent_state or not partner_name:
        await cl.Message(content="Erreur: Agent non initialisé. Veuillez rafraîchir.").send()
        return

    human_message_obj = HumanMessage(content=msg_event.content, id=str(uuid.uuid4()))
    messages_for_lg_agent_input = list(lg_agent_state.get("messages", [])) + [human_message_obj]

    config_for_run = RunnableConfig(
        configurable={"thread_id": active_thread_id, "session_id": active_thread_id, "partner": partner_name}
    )

    # FIX: Explicitly set parent_id=None to make this a top-level message
    # and avoid race conditions with the user message step.
    assistant_final_msg = cl.Message(content="", author="Assistant", parent_id=None)
    all_plot_elements = []

    # --- State tracking for this turn ---
    assistant_msg_sent = False
    tool_steps: Dict[str, cl.Step] = {}
    messages_from_current_run = []
    accumulated_content = ""
    current_run_lg_agent_state_dict = None
    agent_plan_updated = False

    try:
        # FIX: Create a single, top-level step for the entire agent run.
        # name is changed as requested to reduce nesting.
        # parent_id=None is the critical change to prevent ForeignKeyViolationError.
        async with cl.Step(name="🤔 Agent Plan & Execution", type="run", parent_id=None) as agent_run_step:
            agent_run_step.input = msg_event.content
            
            async for stream_mode, chunk in lg_agent.astream(
                {"messages": messages_for_lg_agent_input},
                config=config_for_run,
                stream_mode=["updates", "messages"]
            ):
                if stream_mode == "updates":
                    if not isinstance(chunk, dict):
                        continue
                    
                    if "agent" in chunk and "messages" in chunk["agent"]:
                        current_run_lg_agent_state_dict = chunk["agent"]
                        for msg in chunk["agent"]["messages"]:
                            if msg not in messages_from_current_run:
                                messages_from_current_run.append(msg)

                            if isinstance(msg, AIMessage) and msg.tool_calls:
                                # Update the main step's output to show the plan, but only once.
                                if not agent_plan_updated:
                                    tool_names = [get_tool_display_name(tc["name"]) for tc in msg.tool_calls]
                                    agent_run_step.output = f"Planning to use: {', '.join(tool_names)}"
                                    await agent_run_step.update()
                                    agent_plan_updated = True

                                for tool_call in msg.tool_calls:
                                    tool_id = tool_call["id"]
                                    if tool_id not in tool_steps:
                                        # Parent the tool step to our stable, top-level run step.
                                        tool_step = cl.Step(
                                            name=f"Tool: {get_tool_display_name(tool_call['name'])}",
                                            type="tool",
                                            parent_id=agent_run_step.id
                                        )
                                        tool_step.input = f"```json\n{json.dumps(tool_call.get('args', {}), indent=2, ensure_ascii=False)}\n```"
                                        await tool_step.send()
                                        tool_steps[tool_id] = tool_step

                    elif "tools" in chunk:
                        current_run_lg_agent_state_dict = chunk["tools"]
                        
                        if "messages" in chunk["tools"]:
                            for msg in chunk["tools"]["messages"]:
                                if msg not in messages_from_current_run:
                                    messages_from_current_run.append(msg)
                                if isinstance(msg, ToolMessage):
                                    tc_id = msg.tool_call_id
                                    if tc_id in tool_steps:
                                        ts = tool_steps[tc_id]
                                        if ts.output: continue
                                        
                                        content = _parse_tool_content(msg.content)
                                        if isinstance(content, dict) and "error" in content:
                                            ts.output = f"❌ Error: {content['error']}"
                                            ts.is_error = True
                                        else:
                                            out_str = json.dumps(content, indent=2, ensure_ascii=False) if isinstance(content, (dict, list)) else str(content)
                                            ts.output = f"✅ Success\n```json\n{out_str}```"
                                        await ts.update()

                        # Enhanced plot handling with PlotTracker
                        if "output_image_paths" in chunk["tools"]:
                            print(f"[DEBUG] Available Images: {chunk['tools']['output_image_paths']}")
                            
                            # Use the enhanced plot handler
                            new_plot_elements = await handle_plots_with_tracker(
                                sandbox_client=sandbox_client,
                                active_thread_id=active_thread_id,
                                plot_paths=chunk["tools"]["output_image_paths"],
                                plot_tracker=plot_tracker,
                                turn_id=turn_id
                            )
                            
                            all_plot_elements.extend(new_plot_elements)
                            
                            # Log tracking stats
                            current_turn_plots = plot_tracker.get_turn_plots(turn_id)
                            print(f"[DEBUG] Turn {turn_id}: {len(current_turn_plots)} new plots, {len(all_plot_elements)} total elements")

                elif stream_mode == "messages":
                    message, _ = chunk
                    if isinstance(message, AIMessage) and message.content:
                        delta = message.content
                        if not assistant_msg_sent:
                            await assistant_final_msg.send()
                            assistant_msg_sent = True
                        await assistant_final_msg.stream_token(delta)
                        accumulated_content += delta

    except Exception as e_stream:
        print(f"[ERROR CL STREAM] {e_stream}", file=sys.stdout)
        traceback.print_exc()
        await cl.Message(content=f"An unexpected error occurred: {e_stream}").send()
        return

    finally:
        # Final message handling with enhanced plot information
        if all_plot_elements:
            assistant_final_msg.elements = all_plot_elements
            
            # Log plot summary
            current_turn_plots = plot_tracker.get_turn_plots(turn_id)
            plot_summary = f"Generated {len(current_turn_plots)} new visualization(s) this turn."
            print(f"[DEBUG] {plot_summary}")

        if not assistant_msg_sent and (accumulated_content or all_plot_elements):
            if all_plot_elements and not accumulated_content:
                current_turn_plots = plot_tracker.get_turn_plots(turn_id)
                assistant_final_msg.content = f"Generated {len(current_turn_plots)} visualization(s)."
            else:
                assistant_final_msg.content = accumulated_content
            await assistant_final_msg.send()
        elif assistant_msg_sent:
            await assistant_final_msg.update()

        # Session state persistence with enhanced tracking
        if lg_agent_state is not None and current_run_lg_agent_state_dict is not None:
            merged = lg_agent_state.get("messages", []) + ensure_message_ids(messages_from_current_run)
            lg_agent_state["messages"], _ = remove_duplicate_messages(merged)
            if "output_image_paths" in current_run_lg_agent_state_dict:
                lg_agent_state["output_image_paths"] = current_run_lg_agent_state_dict["output_image_paths"]
            cl.user_session.set("lg_agent_state", lg_agent_state)

        # Clean up old plots periodically (every 5 turns)
        plot_tracker.clear_old_plots(keep_last_n_turns=5)
        
        # Update plot tracker in session
        cl.user_session.set("plot_tracker", plot_tracker)
        
        # Log final tracking stats
        latest_plots = plot_tracker.get_latest_plots(limit=3)
        print(f"[DEBUG] Latest plots: {[p.display_name for p in latest_plots]}")

    print(f"[DEBUG CL] === Turn End for Thread: {active_thread_id} ===\n")