{"data": [{"hovertemplate": "<b>%{x}</b><br>Consumption: %{y:.0f} kWh", "line": {"color": "#4caf50", "width": 4}, "marker": {"color": "#4caf50", "colorscale": [[0.0, "#440154"], [0.1111111111111111, "#482878"], [0.2222222222222222, "#3e4989"], [0.3333333333333333, "#31688e"], [0.4444444444444444, "#26828e"], [0.5555555555555556, "#1f9e89"], [0.6666666666666666, "#35b779"], [0.7777777777777778, "#6ece58"], [0.8888888888888888, "#b5de2b"], [1.0, "#fde725"]], "line": {"color": "#55566a", "width": 1.2}, "size": 10}, "mode": "lines+markers", "name": "Monthly Consumption", "x": ["Jul 2024", "Aug 2024", "Sep 2024", "Oct 2024", "Nov 2024", "Dec 2024", "Jan 2025", "Feb 2025", "Mar 2025", "Apr 2025", "May 2025", "Jun 2025"], "y": {"dtype": "f8", "bdata": "ZZHJmfEwkEFzg/0XsAa0QcaKGkcObKzBtulZRE2ti0FTGTD2rJCPQe741SQAnpBBbVdobSHpkEF1QXhDGsGMQc0fIzen54hBu/tiLJxejUEJUFdHHKG2QZ4pwAP2qtRB"}, "type": "scatter"}, {"hoverinfo": "skip", "line": {"color": "#2196f3", "dash": "dot", "width": 2.5}, "mode": "lines", "name": "Smoothed Trend", "showlegend": true, "x": ["Jul 2024", "Aug 2024", "Sep 2024", "Oct 2024", "Nov 2024", "Dec 2024", "Jan 2025", "Feb 2025", "Mar 2025", "Apr 2025", "May 2025", "Jun 2025"], "y": {"dtype": "f8", "bdata": "dv70cGBYnUHLlMu/puuUQYBk92P/sXlBYFbkRGkAbEGjvkt5JwWFQUR2osv6C45B2vuKRkx0j0GpY/ZapEiQQeia8iClZZlBQrZ0r29yr0FVNO8m+qfBQdgosdddQctB"}, "type": "scatter", "marker": {"color": "#2196f3"}}, {"marker": {"color": "#ff9800", "size": 20, "symbol": "diamond"}, "mode": "markers+text", "name": "Max", "showlegend": true, "text": ["Max"], "textposition": "top right", "x": ["Jun 2025"], "y": [1386993679.00254], "type": "scatter"}, {"marker": {"color": "#388e3c", "size": 20, "symbol": "diamond-open"}, "mode": "markers+text", "name": "Min", "showlegend": true, "text": ["Min"], "textposition": "bottom left", "x": ["Sep 2024"], "y": [-238421795.55184], "type": "scatter"}], "layout": {"annotations": [{"arrowcolor": "#ef553b", "bgcolor": "rgba(255,255,255,0.95)", "font": {"color": "#ef553b", "size": 13}, "showarrow": true, "text": "Peak: 1386993679 kWh", "x": "Jun 2025", "y": 1386993679.00254, "yshift": 35}, {"arrowcolor": "#00cc96", "bgcolor": "rgba(255,255,255,0.95)", "font": {"color": "#00cc96", "size": 13}, "showarrow": true, "text": "Trough: -238421795 kWh", "x": "Sep 2024", "y": -238421795.55184, "yshift": -40}, {"showarrow": false, "text": "<span style=\"color:#888a97;font-size:15px\">Interactive line with smoothed trend and seasonal context highlights major consumption events.</span>", "x": 0.5, "xanchor": "center", "xref": "paper", "y": 1.12, "yanchor": "top", "yref": "paper"}], "hoverlabel": {"bgcolor": "white", "font": {"size": 13}}, "legend": {"orientation": "h", "x": 0.5, "xanchor": "center", "y": 1.06, "yanchor": "bottom"}, "margin": {"b": 70, "l": 40, "r": 32, "t": 80}, "plot_bgcolor": "rgba(243,243,255,0.97)", "shapes": [{"fillcolor": "rgba(231, 244, 253, 0.3)", "layer": "below", "line": {"width": 0}, "type": "rect", "x0": "Jul 2024", "x1": "Sep 2024", "xref": "x", "y0": 0, "y1": 1, "yref": "paper"}, {"fillcolor": "rgba(255, 237, 213, 0.2)", "layer": "below", "line": {"width": 0}, "type": "rect", "x0": "Oct 2024", "x1": "Dec 2024", "xref": "x", "y0": 0, "y1": 1, "yref": "paper"}, {"fillcolor": "rgba(231, 244, 253, 0.3)", "layer": "below", "line": {"width": 0}, "type": "rect", "x0": "Jan 2025", "x1": "Mar 2025", "xref": "x", "y0": 0, "y1": 1, "yref": "paper"}, {"fillcolor": "rgba(255, 237, 213, 0.2)", "layer": "below", "line": {"width": 0}, "type": "rect", "x0": "Apr 2025", "x1": "Jun 2025", "xref": "x", "y0": 0, "y1": 1, "yref": "paper"}], "template": {"data": {"bar": [{"hovertemplate": "<b>%{x}</b><br>%{y}<extra></extra>", "marker": {"color": "#4caf50", "line": {"color": "rgb(237,237,237)", "width": 0.5}, "opacity": 0.9, "pattern": {"shape": "", "fillmode": "overlay", "size": 10, "solidity": 0.2}}, "textfont": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "textposition": "auto", "type": "bar", "error_x": {"color": "rgb(51,51,51)"}, "error_y": {"color": "rgb(51,51,51)"}}], "box": [{"boxmean": "sd", "fillcolor": "#c8e6c9", "line": {"color": "#388e3c", "width": 2}, "marker": {"color": "#4caf50", "line": {"color": "#388e3c", "width": 1}, "opacity": 0.8, "size": 8}, "notched": true, "whiskerwidth": 0.5, "type": "box"}], "candlestick": [{"decreasing": {"fillcolor": "#f44336", "line": {"color": "#f44336", "width": 1}}, "increasing": {"fillcolor": "#4caf50", "line": {"color": "#4caf50", "width": 1}}, "line": {"width": 1}, "whiskerwidth": 0, "type": "candlestick"}], "contour": [{"colorbar": {"bgcolor": "#ffffff", "bordercolor": "#d0d0d0", "borderwidth": 1, "len": 0.7, "thickness": 15, "outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "contours": {"labelfont": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "labelformat": ".2f", "showlabels": true}, "line": {"smoothing": 0.85, "width": 1}, "type": "contour"}], "funnel": [{"connector": {"line": {"color": "#e0e0e0", "dash": "dot", "width": 2}}, "marker": {"colorscale": [[0.0, "#e8f5e9"], [0.1, "#c8e6c9"], [0.25, "#a5d6a7"], [0.4, "#81c784"], [0.55, "#66bb6a"], [0.7, "#4caf50"], [0.85, "#388e3c"], [1.0, "#1b5e20"]], "line": {"color": "#ffffff", "width": 2}}, "opacity": 0.9, "textfont": {"color": "#ffffff", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "textinfo": "value+percent initial", "textposition": "inside", "type": "funnel"}], "heatmap": [{"colorbar": {"bgcolor": "#ffffff", "bordercolor": "#d0d0d0", "borderwidth": 1, "len": 0.7, "thickness": 15, "tickfont": {"color": "#616161", "size": 11}, "title": {"font": {"color": "#212121", "size": 11}, "side": "right"}, "outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "hovertemplate": "X: %{x}<br>Y: %{y}<br>Value: %{z}<extra></extra>", "type": "heatmap"}], "histogram2d": [{"colorbar": {"bgcolor": "#ffffff", "bordercolor": "#d0d0d0", "borderwidth": 1, "len": 0.7, "thickness": 15, "outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "hovertemplate": "X: %{x}<br>Y: %{y}<br>Count: %{z}<extra></extra>", "type": "histogram2d"}], "histogram": [{"histnorm": "", "hovertemplate": "Range: %{x}<br>Count: %{y}<extra></extra>", "marker": {"color": "#66bb6a", "line": {"color": "#388e3c", "width": 1}, "opacity": 0.85, "pattern": {"shape": "", "fillmode": "overlay", "size": 10, "solidity": 0.2}}, "nbinsx": 20, "type": "histogram"}], "indicator": [{"delta": {"font": {"size": 16}, "reference": 50}, "gauge": {"axis": {"range": [null, null], "tickcolor": "#616161", "tickfont": {"color": "#616161", "size": 11}, "tickwidth": 1}, "bar": {"color": "#4caf50", "thickness": 0.8}, "bgcolor": "#e8f5e9", "bordercolor": "#81c784", "borderwidth": 2, "steps": [{"color": "#c8e6c9", "range": [0, 50]}, {"color": "#a5d6a7", "range": [50, 80]}, {"color": "#81c784", "range": [80, 100]}], "threshold": {"line": {"color": "#f44336", "width": 4}, "thickness": 0.75, "value": 90}}, "mode": "gauge+number+delta", "number": {"font": {"color": "#388e3c", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 30.0}, "suffix": "", "valueformat": ".1f"}, "title": {"font": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 16}}, "type": "indicator"}], "parcoords": [{"labelfont": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "line": {"cmax": 1, "cmin": 0, "color": "#4caf50", "colorscale": [[0.0, "#e8f5e9"], [0.1, "#c8e6c9"], [0.25, "#a5d6a7"], [0.4, "#81c784"], [0.55, "#66bb6a"], [0.7, "#4caf50"], [0.85, "#388e3c"], [1.0, "#1b5e20"]], "showscale": true, "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "rangefont": {"color": "#616161", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "tickfont": {"color": "#616161", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "type": "parcoords"}], "pie": [{"hole": 0, "hovertemplate": "<b>%{label}</b><br>%{value}<br>%{percent}<extra></extra>", "marker": {"colors": ["#4caf50", "#2196f3", "#ff9800", "#388e3c", "#9c27b0", "#81c784", "#009688", "#2e7d32", "#f44336", "#a5d6a7"], "line": {"color": "#ffffff", "width": 2}}, "pull": [0.05, 0, 0, 0, 0, 0, 0, 0, 0, 0], "textfont": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "textinfo": "label+percent", "textposition": "auto", "type": "pie", "automargin": true}], "sankey": [{"link": {"arrowlen": 15, "color": "rgba(76, 175, 80, 0.4)", "hovertemplate": "%{source.label} → %{target.label}<br>%{value}<extra></extra>"}, "node": {"color": "#66bb6a", "hoverlabel": {"font": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}}, "line": {"color": "#388e3c", "width": 1}, "pad": 20, "thickness": 25}, "type": "sankey"}], "scatter3d": [{"line": {"color": "#4caf50", "width": 4, "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "marker": {"color": "#4caf50", "colorscale": [[0.0, "#e8f5e9"], [0.1, "#c8e6c9"], [0.25, "#a5d6a7"], [0.4, "#81c784"], [0.55, "#66bb6a"], [0.7, "#4caf50"], [0.85, "#388e3c"], [1.0, "#1b5e20"]], "line": {"color": "#388e3c", "width": 0.5}, "opacity": 0.8, "size": 8, "symbol": "circle", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "projection": {"x": {"opacity": 0.7, "scale": 0.4, "show": true}, "y": {"opacity": 0.7, "scale": 0.4, "show": true}, "z": {"opacity": 0.7, "scale": 0.4, "show": true}}, "type": "scatter3d"}], "scattergl": [{"hovertemplate": "<b>X:</b> %{x}<br><b>Y:</b> %{y}<extra></extra>", "marker": {"color": "#4caf50", "line": {"color": "#388e3c", "width": 1}, "opacity": 0.8, "size": 10, "symbol": "circle", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "mode": "markers", "type": "scattergl"}], "scatterpolar": [{"fill": "toself", "fillcolor": "rgba(76, 175, 80, 0.2)", "hoveron": "points+fills", "line": {"color": "#4caf50", "shape": "spline", "smoothing": 0.3, "width": 2}, "marker": {"color": "#4caf50", "line": {"color": "#ffffff", "width": 1}, "size": 8, "symbol": "circle", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scatterpolar"}], "scatter": [{"fill": "tozer<PERSON>", "fillcolor": "rgba(76, 175, 80, 0.3)", "hoveron": "points+fills", "hovertemplate": "%{x}<br>%{y}<extra></extra>", "line": {"color": "#4caf50", "shape": "spline", "smoothing": 0.3, "width": 2}, "mode": "lines", "type": "scatter", "fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}], "sunburst": [{"insidetextorientation": "radial", "leaf": {"opacity": 0.9}, "marker": {"colorscale": [[0.0, "#e8f5e9"], [0.1, "#c8e6c9"], [0.25, "#a5d6a7"], [0.4, "#81c784"], [0.55, "#66bb6a"], [0.7, "#4caf50"], [0.85, "#388e3c"], [1.0, "#1b5e20"]], "line": {"color": "#ffffff", "width": 2}}, "textfont": {"color": "#ffffff", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "type": "sunburst"}], "surface": [{"colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "contours": {"x": {"color": "#e0e0e0", "show": true, "width": 1}, "y": {"color": "#e0e0e0", "show": true, "width": 1}, "z": {"color": "#e0e0e0", "show": true, "width": 1}}, "lighting": {"ambient": 0.5, "diffuse": 0.6, "fresnel": 0.4, "roughness": 0.5, "specular": 0.2}, "lightposition": {"x": -1000, "y": 1000, "z": 1000}, "type": "surface", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}], "table": [{"cells": {"align": ["left", "center"], "fill": {"color": "rgb(237,237,237)"}, "font": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "height": 30, "line": {"color": "white", "width": 1}, "values": []}, "header": {"align": ["left", "center"], "fill": {"color": "rgb(217,217,217)"}, "font": {"color": "#ffffff", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 13}, "height": 35, "line": {"color": "white", "width": 1}, "values": []}, "type": "table"}], "treemap": [{"marker": {"colorscale": [[0.0, "#e8f5e9"], [0.1, "#c8e6c9"], [0.25, "#a5d6a7"], [0.4, "#81c784"], [0.55, "#66bb6a"], [0.7, "#4caf50"], [0.85, "#388e3c"], [1.0, "#1b5e20"]], "line": {"color": "#ffffff", "width": 2}, "pad": {"b": 2, "l": 2, "r": 2, "t": 25}}, "pathbar": {"textfont": {"family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "thickness": 20, "visible": true}, "textfont": {"color": "#ffffff", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "textposition": "middle center", "type": "treemap"}], "violin": [{"box": {"visible": true}, "fillcolor": "#c8e6c9", "jitter": 0.05, "line": {"color": "#388e3c", "width": 2}, "marker": {"color": "#4caf50", "line": {"color": "#388e3c", "width": 1}, "opacity": 0.8}, "meanline": {"visible": true}, "opacity": 0.7, "points": "all", "scalemode": "width", "type": "violin"}], "waterfall": [{"connector": {"line": {"color": "#e0e0e0", "dash": "dot", "width": 2}}, "decreasing": {"marker": {"color": "#f44336", "line": {"color": "darkred", "width": 1}}}, "increasing": {"marker": {"color": "#4caf50", "line": {"color": "#388e3c", "width": 1}}}, "textfont": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "totals": {"marker": {"color": "#388e3c", "line": {"color": "#1b5e20", "width": 1}}}, "type": "waterfall"}], "barpolar": [{"marker": {"line": {"color": "rgb(237,237,237)", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "rgb(51,51,51)", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "rgb(51,51,51)"}, "baxis": {"endlinecolor": "rgb(51,51,51)", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "rgb(51,51,51)"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "type": "choropleth"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "type": "contourcarpet"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "type": "mesh3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scattergeo"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scattermapbox"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scattermap"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scatterternary"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#616161", "arrowhead": 0, "arrowsize": 1, "arrowwidth": 1, "bgcolor": "rgba(255, 255, 255, 0.9)", "bordercolor": "#d0d0d0", "borderpad": 4, "borderwidth": 1, "font": {"color": "#212121", "size": 11}}, "bargap": 0.2, "bargroupgap": 0.1, "colorscale": {"diverging": [[0.0, "#f44336"], [0.25, "#ffcdd2"], [0.5, "#ffffff"], [0.75, "#c8e6c9"], [1.0, "#388e3c"]], "sequential": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "sequentialminus": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]]}, "colorway": ["#F8766D", "#A3A500", "#00BF7D", "#00B0F6", "#E76BF3"], "dragmode": "zoom", "font": {"color": "rgb(51,51,51)", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 13}, "hoverlabel": {"align": "left", "bgcolor": "#00211b", "bordercolor": "#4caf50", "font": {"color": "#ffffff", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}}, "legend": {"bgcolor": "rgba(255, 255, 255, 0.95)", "bordercolor": "#d0d0d0", "borderwidth": 1, "font": {"color": "#212121", "size": 11}, "itemsizing": "constant", "itemwidth": 30, "orientation": "h", "tracegroupgap": 5, "traceorder": "normal", "x": 0, "xanchor": "left", "y": 1.02, "yanchor": "bottom"}, "margin": {"b": 60, "l": 80, "r": 60, "t": 80}, "modebar": {"activecolor": "#4caf50", "bgcolor": "rgba(255, 255, 255, 0.9)", "color": "#616161"}, "paper_bgcolor": "white", "plot_bgcolor": "rgb(237,237,237)", "selectdirection": "any", "title": {"font": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 20}, "pad": {"b": 20, "t": 0}, "x": 0.5, "xanchor": "center", "y": 0.98, "yanchor": "top"}, "xaxis": {"gridcolor": "white", "griddash": "dot", "gridwidth": 1, "linecolor": "white", "linewidth": 1, "showgrid": true, "showline": true, "showspikes": true, "spikecolor": "#e0e0e0", "spikedash": "dot", "spikemode": "across", "spikethickness": 1, "tickcolor": "rgb(51,51,51)", "tickfont": {"color": "#616161", "size": 12}, "ticklen": 5, "ticks": "outside", "tickwidth": 1, "title": {"font": {"color": "#212121", "size": 16}, "standoff": 15}, "zeroline": true, "zerolinecolor": "white", "zerolinewidth": 1, "automargin": true}, "yaxis": {"gridcolor": "white", "gridwidth": 1, "linecolor": "white", "linewidth": 1, "showgrid": true, "showline": true, "showspikes": true, "spikecolor": "#e0e0e0", "spikedash": "dot", "spikemode": "across", "spikethickness": 1, "tickcolor": "rgb(51,51,51)", "tickfont": {"color": "#616161", "size": 12}, "ticklen": 5, "ticks": "outside", "tickwidth": 1, "title": {"font": {"color": "#212121", "size": 16}, "standoff": 15}, "zeroline": true, "zerolinecolor": "white", "zerolinewidth": 1, "automargin": true}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "rgb(237,237,237)", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hovermode": "closest", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}, "bgcolor": "rgb(237,237,237)", "radialaxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}}, "scene": {"xaxis": {"backgroundcolor": "rgb(237,237,237)", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "rgb(237,237,237)", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "rgb(237,237,237)", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside", "zerolinecolor": "white"}}, "shapedefaults": {"fillcolor": "black", "line": {"width": 0}, "opacity": 0.3}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}, "baxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}, "bgcolor": "rgb(237,237,237)", "caxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}}}}, "title": {"font": {"size": 20}, "text": "Monthly Electricity Consumption – Trend Line", "x": 0.5, "y": 0.92}, "xaxis": {"showgrid": false, "tickangle": -25, "tickfont": {"size": 12}}, "yaxis": {"gridcolor": "#d0daee", "title": {"text": "Electricity Consumption (kWh)"}, "zerolinecolor": "#adb0c6"}, "autosize": true}}