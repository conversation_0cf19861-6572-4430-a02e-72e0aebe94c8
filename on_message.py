# stream text and tools
@cl.on_message
async def on_message(msg_event: cl.Message):
    active_thread_id = cl.context.session.thread_id
    if not active_thread_id:
        print(f"[CRITICAL CL] cl.context.session.thread_id is None in on_message!")
        await cl.Message(content="Erreur critique: Impossible d'identifier la session.").send()
        cl.user_session.set("langgraph_initialized_for_thread", False)
        return

    # Get the set of already displayed plots for this session
    displayed_plots_session = cl.user_session.get("displayed_plot_filenames", set())

    if not cl.user_session.get("langgraph_initialized_for_thread") or \
       cl.user_session.get("thread_id") != active_thread_id:
        print(f"[DEBUG CL] Initializing/Re-syncing LangGraph for thread_id: {active_thread_id}")
        partner_name_for_init = cl.user_session.get("partner_name", os.getenv("DEFAULT_PARTNER", "oksigen"))
        await initialize_langgraph_components(active_thread_id, partner_name_for_init)

    lg_agent = cl.user_session.get("lg_agent")
    lg_agent_state: Optional[AgentState] = cl.user_session.get("lg_agent_state")
    partner_name = cl.user_session.get("partner_name")

    print(f"\n[DEBUG CL] === Turn Start for Thread: {active_thread_id}, User Msg ID: {msg_event.id} ===")

    sandbox_client: Optional[SandboxClient] = cl.user_session.get("sandbox_client")
    if not sandbox_client:
        try:
            sandbox_client = SandboxClient()
            cl.user_session.set("sandbox_client", sandbox_client)
            print("[DEBUG CL] SandboxClient instantiated.")
        except Exception as e:
            print(f"[ERROR CL] Failed to instantiate SandboxClient: {e}")
            await cl.Message(content="Erreur de configuration du client Sandbox.").send()
            return

    if not lg_agent or not lg_agent_state or not partner_name:
        await cl.Message(content="Erreur: Agent non initialisé. Veuillez rafraîchir.").send()
        print(f"[ERROR CL] Crucial LangGraph components missing for thread {active_thread_id}.")
        return

    human_message_obj = HumanMessage(content=msg_event.content, id=str(uuid.uuid4()))
    current_messages_for_input = list(lg_agent_state.get("messages", []))
    messages_for_lg_agent_input = current_messages_for_input + [human_message_obj]

    config_for_run = RunnableConfig(
        configurable={"thread_id": active_thread_id, "session_id": active_thread_id, "partner": partner_name}
    )

    # The main message for the AI's final text response
    assistant_final_msg = cl.Message(content="")

    # These are for the current turn's processing
    processed_plot_filenames_this_turn = set()
    generated_plot_paths_this_turn = []

    # Track tool steps and streaming state
    tool_steps: Dict[str, cl.Step] = {}
    current_step = None
    assistant_msg_sent = False
    streamed = False

    # For state management
    messages_from_current_run = []

    try:
        async with cl.Step(name="Processing Request", type="chain") as root_step:
            root_step.input = msg_event.content
            current_step = root_step

            print("[DEBUG CL] Starting graph stream processing with messages mode")

            async for step in lg_agent.astream(
                {"messages": messages_for_lg_agent_input},
                config=config_for_run,
                stream_mode="messages"
            ):
                node = step[1].get("langgraph_node") if len(step) > 1 else None
                message = step[0] if len(step) > 0 else None

                print(f"[DEBUG STREAM] Node: {node}, Message type: {type(message)}")

                if isinstance(message, AIMessage):
                    messages_from_current_run.append(message)

                    if node == "call_model" or node == "agent":
                        if current_step == root_step:
                            reasoning_step = cl.Step(name="🤔 Agent Reasoning", type="llm")
                            await reasoning_step.send()
                            current_step = reasoning_step

                    if hasattr(message, "tool_calls") and message.tool_calls:
                        print(f"[DEBUG TOOL] Tool calls detected: {[tc.get('name') for tc in message.tool_calls]}")

                        ### <<< CHANGE START: Improved Tool Selection Step Display >>>
                        tool_select_step = cl.Step(name="🛠️ Tool Selection", type="tool_selection")
                        tool_calls_content = ""
                        for tool_call in message.tool_calls:
                            tool_name = tool_call.get("name", "Unknown Tool")
                            # Use raw name as a fallback if display name is empty
                            tool_display = get_tool_display_name(tool_name) or tool_name
                            args = tool_call.get("args", {})
                            args_str = json.dumps(args, indent=2, ensure_ascii=False)
                            tool_calls_content += f"**Calling Tool: `{tool_display}`**\n"
                            # Only show arguments if they are not empty
                            if args:
                                tool_calls_content += f"```json\n{args_str}\n```\n"
                        tool_select_step.output = tool_calls_content
                        await tool_select_step.send()
                        ### <<< CHANGE END >>>

                        for tool_call in message.tool_calls:
                            tool_name = tool_call["name"]
                            tool_id = tool_call["id"]
                            if tool_id not in tool_steps:
                                tool_step = cl.Step(
                                    name=get_tool_display_name(tool_name) or tool_name,
                                    type="tool"
                                )
                                await tool_step.send()
                                tool_steps[tool_id] = tool_step

                                tool_args = tool_call.get("args", {})
                                if tool_args:
                                    tool_input = json.dumps(tool_args, indent=2, ensure_ascii=False)
                                    tool_step.input = tool_input
                                    await tool_step.update()

                    if message.content:
                        content_to_stream = ""
                        if isinstance(message.content, str):
                            content_to_stream = message.content
                        elif isinstance(message.content, list):
                            for part in message.content:
                                if isinstance(part, dict) and "text" in part:
                                    content_to_stream += part["text"]
                                elif isinstance(part, str):
                                    content_to_stream += part

                        if content_to_stream:
                            if not assistant_msg_sent:
                                await assistant_final_msg.send()
                                assistant_msg_sent = True

                            await assistant_final_msg.stream_token(content_to_stream)
                            await current_step.stream_token(content_to_stream)
                            streamed = True
                            print(f"[DEBUG STREAM] Streamed content: {content_to_stream[:100]}...")

                elif isinstance(message, ToolMessage):
                    messages_from_current_run.append(message)
                    tool_call_id = message.tool_call_id
                    print(f"[DEBUG TOOL] Tool message received, ID: {tool_call_id}")

                    if tool_call_id in tool_steps:
                        tool_step = tool_steps[tool_call_id]
                        tool_content = _parse_tool_content(message.content)

                        if isinstance(tool_content, dict):
                            output_summary = json.dumps(tool_content, indent=2)
                            if "error" in tool_content:
                                tool_step.output = f"❌ Error: {tool_content.get('error', 'Unknown error')}"
                                tool_step.is_error = True
                            else:
                                tool_step.output = f"✅ Success:\n```json\n{output_summary}\n```"

                            ### <<< CHANGE START: Corrected Plot Handling Logic >>>
                            if "output_image_paths" in tool_content and tool_content["output_image_paths"]:
                                plot_elements_from_tool = []
                                # Create a new step just for this plot generation task
                                plot_gen_step = cl.Step(name="📊 Generating Visualizations", type="tool_result")
                                await plot_gen_step.send()

                                for plot_path in tool_content["output_image_paths"]:
                                    if plot_path in displayed_plots_session or plot_path in processed_plot_filenames_this_turn:
                                        continue

                                    try:
                                        pickle_bytes = await sandbox_client.download_plot(
                                            session_id=active_thread_id,
                                            plot_name=plot_path,
                                        )
                                        if pickle_bytes:
                                            fig_obj = pickle.loads(pickle_bytes)
                                            fig_obj = apply_company_style(fig_obj)
                                            plot_elem = cl.Plotly(
                                                name=os.path.basename(plot_path).rsplit(".", 1)[0],
                                                figure=fig_obj,
                                                display="inline",
                                            )
                                            plot_elements_from_tool.append(plot_elem)
                                            generated_plot_paths_this_turn.append(plot_path) # Track for state
                                            displayed_plots_session.add(plot_path)
                                            processed_plot_filenames_this_turn.add(plot_path)
                                            print(f"[DEBUG PLOT] Created plot element for: {plot_path}")

                                    except Exception as e:
                                        print(f"[ERROR PLOT] Error processing plot {plot_path}: {e}")
                                        plot_gen_step.output = f"❌ Error generating visualization: {str(e)}"
                                        plot_gen_step.is_error = True
                                        await plot_gen_step.update()

                                if plot_elements_from_tool:
                                    # Send the plots in a new, dedicated message IMMEDIATELY
                                    plot_message = cl.Message(
                                        author="Visualisation",
                                        content="",
                                        elements=plot_elements_from_tool
                                    )
                                    await plot_message.send()
                                    plot_gen_step.output = f"✅ Generated {len(plot_elements_from_tool)} visualization(s)."
                                    await plot_gen_step.update()
                            ### <<< CHANGE END >>>

                        else: # Handle non-dict tool content
                            tool_step.output = f"✅ {str(message.content)}"

                        await tool_step.update()

                elif isinstance(message, HumanMessage):
                    messages_from_current_run.append(message)
                    print(f"[DEBUG STREAM] Human message: {message.content[:50]}...")

            root_step.output = assistant_final_msg.content

        cl.user_session.set("displayed_plot_filenames", displayed_plots_session)

        if not streamed and not assistant_final_msg.content:
             # Only show this if no text was streamed and no plots were made
            if not generated_plot_paths_this_turn:
                assistant_final_msg.content = "⚠️ No response generated."
                print("[DEBUG STREAM] No content was streamed and no plots were generated")

        if lg_agent_state is not None:
            lg_state_msgs = lg_agent_state.get("messages", []) + ensure_message_ids(messages_from_current_run)
            lg_agent_state["messages"], _ = remove_duplicate_messages(lg_state_msgs)

            if generated_plot_paths_this_turn:
                current_plot_paths = lg_agent_state.get("output_image_paths", [])
                lg_agent_state["output_image_paths"] = list(set(current_plot_paths + generated_plot_paths_this_turn))
                print(f"[DEBUG PLOT STATE] Updated state with plots: {lg_agent_state['output_image_paths']}")

            cl.user_session.set("lg_agent_state", lg_agent_state)
            print("[DEBUG LG STATE]", pprint.pformat(serialise_state(lg_agent_state), width=100, compact=True))

    except Exception as e_stream:
        print(f"[ERROR CL STREAM] Exception during agent stream: {e_stream}")
        traceback.print_exc(file=sys.stdout)
        error_msg = cl.Message(content=f"An error occurred: {e_stream}")
        await error_msg.send()

    finally:
        if not assistant_msg_sent and assistant_final_msg.content:
            await assistant_final_msg.send()
        elif assistant_msg_sent:
            await assistant_final_msg.update()

        print(f"[DEBUG CL] Processing completed for thread: {active_thread_id}")
        print(f"[DEBUG CL] === Turn End for Thread: {active_thread_id} ===\n")
